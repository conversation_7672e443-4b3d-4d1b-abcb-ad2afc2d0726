export interface HelpCenterNavItem {
  label: string;
  activeIcon: string;
  inactiveIcon: string;
}

export const HELP_CENTER_NAV: HelpCenterNavItem = {
  label: 'Help Center',
  activeIcon: 'assets/images/side-nav/help-active.svg',
  inactiveIcon: 'assets/images/side-nav/message.svg'
};

export interface User {
  name: string;
  username: string;
  email?: string;
  avatar?: string;
}

export const USERS_LIST: User[] = [
  { name: '<PERSON>kash', username: 'akash', email: '<EMAIL>' },
  { name: 'Amit', username: 'amit', email: '<EMAIL>' },
  { name: 'Amit Nagpal', username: 'amit_nagpal', email: '<EMAIL>' },
  { name: 'Priya', username: 'priya', email: '<EMAIL>' },
  { name: 'Sarim', username: 'sarim', email: '<EMAIL>' },
  { name: 'CAX Admin', username: 'cax_admin', email: '<EMAIL>' },
  { name: '<PERSON>', username: 'alice', email: '<EMAIL>' },
  { name: '<PERSON>', username: 'bob', email: '<EMAIL>' },
  { name: '<PERSON> <PERSON>e', username: 'john_doe', email: '<EMAIL>' },
  { name: 'Jane Smith', username: 'jane_smith', email: '<EMAIL>' }
];

export type TicketType = 'added' | 'updated' | 'comment' | 'cancelled';
export type TicketStatus = 'open' | 'closed';

export interface TicketComment {
  user: string;
  text: string;
  date: string;
  files?: { name: string; url: string; type: string }[];
}

export interface HelpCentreTicket {
    id: string;
    iid: number;
    project_id: number;
    type: TicketType;
    ticketId: string;
    title: string;
    message: string;
    description?: string;
    user?: string;
    author?: any;
    date: string;
    updated_at: string;
    status: TicketStatus;
    read: boolean;
    assignedTo?: string;
    category?: string;
    labels?: string[];
    comments?: TicketComment[];
}

export interface TabConfig {
    id: string;
    header: string;
    filterKey?: 'all' | 'user';
}

export const HELP_CENTRE_TABS: TabConfig[] = [
    {
        id: 'all',
        header: 'All Tickets',
        filterKey: 'all',
    },
    {
        id: 'my-tickets',
        header: 'My Tickets',
        filterKey: 'user',
    },
];
