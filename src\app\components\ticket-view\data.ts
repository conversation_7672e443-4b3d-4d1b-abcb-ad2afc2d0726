export interface HelpCenterNavItem {
  label: string;
  activeIcon: string;
  inactiveIcon: string;
}

export const HELP_CENTER_NAV: HelpCenterNavItem = {
  label: 'Help Center',
  activeIcon: 'assets/images/side-nav/help-active.svg',
  inactiveIcon: 'assets/images/side-nav/message.svg'
};

export interface User {
  name: string;
  username: string;
  email?: string;
  avatar?: string;
}

export const USERS_LIST: User[] = [
  { name: '<PERSON>kash', username: 'akash', email: '<EMAIL>' },
  { name: 'Amit', username: 'amit', email: '<EMAIL>' },
  { name: 'Amit Nagpal', username: 'amit_nagpal', email: '<EMAIL>' },
  { name: 'Priya', username: 'priya', email: '<EMAIL>' },
  { name: 'Sarim', username: 'sarim', email: '<EMAIL>' },
  { name: 'CAX Admin', username: 'cax_admin', email: '<EMAIL>' },
  { name: '<PERSON>', username: 'alice', email: '<EMAIL>' },
  { name: '<PERSON>', username: 'bob', email: '<EMAIL>' },
  { name: '<PERSON> <PERSON>e', username: 'john_doe', email: '<EMAIL>' },
  { name: 'Jane Smith', username: 'jane_smith', email: '<EMAIL>' }
];

export type TicketType = 'added' | 'updated' | 'comment' | 'cancelled';
export type TicketStatus = 'open' | 'closed';

export interface TicketComment {
  user: string;
  text: string;
  date: string;
  files?: { name: string; url: string; type: string }[];
}

export interface HelpCentreTicket {
    id: string;
    iid: number;
    project_id: number;
    type: TicketType;
    ticketId: string;
    title: string;
    message: string;
    description?: string;
    user?: string;
    author?: any;
    date: string;
    updated_at: string;
    status: TicketStatus;
    read: boolean;
    assignedTo?: string;
    category?: string;
    labels?: string[];
    comments?: TicketComment[];
}

export interface TabConfig {
    id: string;
    header: string;
    filterKey?: 'all' | 'user';
}

export const HELP_CENTRE_TABS: TabConfig[] = [
    {
        id: 'all',
        header: 'All Tickets',
        filterKey: 'all',
    },
    {
        id: 'my-tickets',
        header: 'My Tickets',
        filterKey: 'user',
    },
];

export const HELP_CENTRE_TICKETS: HelpCentreTicket[] = [
    {
        id: '1',
        iid: 1,
        project_id: 1,
        type: 'added',
        ticketId: 'AD24AR001',
        title: 'New SKU Addition Request',
        message: 'Turk New SKUs (AD24AR001) was added.',
        user: 'Akash',
        date: '2024-03-23',
        updated_at: '2024-03-23T21:42:00',
        status: 'open',
        read: false,
        category: 'SKU Management',
        comments: [
          { user: 'Akash', text: 'Hi', date: '2024-03-23T21:42:00' },
          { user: 'Akash', text: 'Please check the Manufacturer to be "Prysmian Group" MFR code 20322', date: '2024-03-23T21:42:10' },
          { user: 'CAX Admin', text: 'While exploring batch: X1KL250 (Panduit), we discovered a total of 466 products within this batch. However, upon further investigation, we found that 464 products have already been successfully delivered in the MLf6920 batch with same country of sale. We kindly request your confirmation that we should only process the 2 new products.', date: '2024-03-23T21:42:20' },
          { user: 'Akash', text: 'Yes, please process only the new products.', date: '2024-03-23T21:42:30' }
        ]
    },
    {
        id: '2',
        iid: 2,
        project_id: 2,
        type: 'updated',
        ticketId: 'AD24AR002',
        title: 'Batch Status Update',
        message: "Batch status was updated from 'In Progress' to 'Awaiting for Customer Response'",
        user: 'Amit',
        date: '2024-03-23',
        updated_at: '2024-03-23T20:10:00',
        status: 'open',
        read: false,
        category: 'Batch Processing',
        comments: [
          { user: 'Amit', text: 'Batch status updated. Please review.', date: '2024-03-23T20:10:00' },
          { user: 'Akash', text: 'Reviewed. Awaiting customer response.', date: '2024-03-23T20:12:00' }
        ]
    },
    {
        id: '3',
        iid: 3,
        project_id: 3,
        type: 'comment',
        ticketId: 'AD24AR003',
        title: 'SKU Details Update',
        message: 'Akash commented "Please look into this."',
        user: 'Akash',
        date: '2024-03-22',
        updated_at: '2024-03-22T18:00:00',
        status: 'open',
        read: true,
        category: 'SKU Management',
        comments: [
          { user: 'Akash', text: 'Please look into this.', date: '2024-03-22T18:00:00' }
        ]
    },
    {
        id: '4',
        iid: 4,
        project_id: 4,
        type: 'comment',
        ticketId: 'AD24AR004',
        title: 'SKU Details Update',
        message: 'Akash commented "Kindly, update this SKU details."',
        user: 'Akash',
        date: '2024-03-21',
        updated_at: '2024-03-21T00:00:00',
        status: 'open',
        read: false,
        category: 'SKU Management',
    },
    {
        id: '5',
        iid: 5,
        project_id: 5,
        type: 'cancelled',
        ticketId: 'AD24AR005',
        title: 'Batch Cancellation',
        message: 'Amit Nagpal cancelled this batch.',
        user: 'Amit Nagpal',
        date: '2024-03-20',
        updated_at: '2024-03-20T00:00:00',
        status: 'closed',
        read: true,
        category: 'Batch Processing',
    },
    {
        id: '6',
        iid: 6,
        project_id: 6,
        type: 'added',
        ticketId: 'AD24AR006',
        title: 'New Batch Added',
        message: 'A new batch (AD24AR006) was added by Priya.',
        user: 'Priya',
        date: '2024-03-19',
        updated_at: '2024-03-19T00:00:00',
        status: 'open',
        read: false,
        category: 'Batch Processing',
    },
    {
        id: '7',
        iid: 7,
        project_id: 7,
        type: 'updated',
        ticketId: 'AD24AR007',
        title: 'Batch Status Update',
        message: "Batch status was updated from 'Awaiting' to 'Completed'",
        user: 'Akash',
        date: '2024-03-18',
        updated_at: '2024-03-18T00:00:00',
        status: 'closed',
        read: true,
        category: 'Batch Processing',
    },
    {
        id: '8',
        iid: 8,
        project_id: 8,
        type: 'comment',
        ticketId: 'AD24AR008',
        title: 'Batch Comment',
        message: 'Priya commented "Batch is ready for review."',
        user: 'Priya',
        date: '2024-03-17',
        updated_at: '2024-03-17T00:00:00',
        status: 'open',
        read: false,
        category: 'Batch Processing',
    },
    {
        id: '9',
        iid: 9,
        project_id: 9,
        type: 'cancelled',
        ticketId: 'AD24AR009',
        title: 'Batch Cancelled',
        message: 'Amit Nagpal cancelled this batch.',
        user: 'Amit Nagpal',
        date: '2024-03-16',
        updated_at: '2024-03-16T00:00:00',
        status: 'closed',
        read: true,
        category: 'Batch Processing',
    },
    {
        id: '10',
        iid: 10,
        project_id: 10,
        type: 'added',
        ticketId: 'AD24AR010',
        title: 'New SKU Addition Request',
        message: 'Priya added new SKUs (AD24AR010).',
        user: 'Priya',
        date: '2024-03-15',
        updated_at: '2024-03-15T00:00:00',
        status: 'open',
        read: false,
        category: 'SKU Management',
    },
    {
        id: '11',
        iid: 11,
        project_id: 11,
        type: 'comment',
        ticketId: 'AD24AR011',
        title: 'Chat Example',
        message: 'Example ticket with both sender and receiver messages.',
        user: 'Akash',
        date: '2024-03-24',
        updated_at: '2024-03-24T00:00:00',
        status: 'open',
        read: false,
        category: 'Demo',
        comments: [
          { user: 'Sarim', text: 'Hi Akash, can you check the new batch?', date: '2024-03-24T09:00:00' },
          { user: 'Akash', text: 'Sure, I will check and update you soon.', date: '2024-03-24T09:01:00' },
          { user: 'Sarim', text: 'Thanks! Please confirm once done.', date: '2024-03-24T09:02:00' },
          { user: 'Akash', text: 'Confirmed. The batch is processed.', date: '2024-03-24T09:03:00' }
        ]
    },
];
