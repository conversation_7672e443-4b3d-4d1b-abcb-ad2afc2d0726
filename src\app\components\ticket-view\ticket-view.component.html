<div class="ticket-view-container">
  <div class="ticket-header-row">
    <div class="ticket-header-title">
      <span>Help-Center</span>
    </div>
    <button mat-raised-button color="primary" (click)="onRaiseTicket()">Raise Ticket</button>
  </div>

  <div class="ticket-tabs-row">
    <div
      *ngFor="let tab of tabs"
      class="ticket-tab"
      [class.active]="selectedTab.id === tab.id"
      (click)="selectTab(tab)"
    >
      {{ tab.header }}
      <span *ngIf="tab.id === 'all' && allTicketsCount > 0" class="tab-badge">{{ allTicketsCount }}</span>
      <span *ngIf="tab.id === 'my-tickets' && myTicketsCount > 0" class="tab-badge">{{ myTicketsCount }}</span>
    </div>
  </div>

  <div class="ticket-list">
    <!-- Empty state when no tickets -->
    <div *ngIf="filteredTickets.length === 0" class="empty-state">
      <div class="empty-state-content">
        <mat-icon class="empty-state-icon">assignment</mat-icon>
        <h3 class="empty-state-title">No tickets found</h3>
        <p class="empty-state-message">
          <span *ngIf="selectedTab.filterKey === 'user'">You haven't created any tickets yet.</span>
          <span *ngIf="selectedTab.filterKey === 'all'">No tickets have been created yet.</span>
        </p>
        <button mat-raised-button color="primary" (click)="onRaiseTicket()" class="empty-state-button">
          Create your first ticket
        </button>
      </div>
    </div>

    <!-- Ticket list items -->
    <div
      *ngFor="let ticket of filteredTickets"
      class="ticket-item"
      [class.selected]="selectedTicketId === ticket.id"
      [class.unread]="!ticket.read"
      (click)="onTicketClick(ticket)"
    >
      <div class="ticket-item-main">
        <div class="ticket-item-type-row">
          <span class="ticket-item-type">{{ ticket.title }}</span>
          <span class="ticket-item-badge status-badge" [ngClass]="ticket.status">{{ ticket.status | titlecase }}</span>
        </div>
        <div class="ticket-item-labels" *ngIf="ticket.labels && ticket.labels.length > 0">
          <mat-chip-list>
            <mat-chip
              *ngFor="let label of ticket.labels"
              class="ticket-label-chip"
              [style.background-color]="getLabelColor(label)"
              [style.color]="getLabelTextColor(label)">
              {{ label }}
            </mat-chip>
          </mat-chip-list>
        </div>
        <div class="ticket-item-message">{{ ticket.message }}</div>
      </div>
      <div class="ticket-item-meta">
        <span class="ticket-item-date">{{ ticket.date | date: 'MMM d' }}</span>
        <span class="ticket-item-user"><span class="by">-- by </span>{{ logDisplayAuthor(ticket) }}</span>
      </div>
    </div>
  </div>
</div>

<!-- Sidebar backdrop -->
<div *ngIf="sidebarOpen" class="sidebar-backdrop" [ngClass]="{'sidebar-open': sidebarOpen}" (click)="onCancel()"></div>

<div class="ticket-sidebar" *ngIf="sidebarOpen" [ngClass]="{'sidebar-open': sidebarOpen}">
  <!-- Header: Always visible -->
  <div class="sidebar-header">
    <div class="header-content">
      <h2 class="header-title">
        <span *ngIf="sidebarMode === 'edit'">Edit Ticket</span>
        <span *ngIf="sidebarMode === 'new'">Raise Ticket</span>
      </h2>
      <button class="header-close-btn" (click)="onCancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Content Area: Show spinner or hide while loading -->
  <div class="sidebar-content">
    <!-- Show group labels as chips -->
    <!-- <div *ngIf="groupLabels.length" class="group-labels-row" style="margin-bottom: 12px; display: flex; flex-wrap: wrap; gap: 8px;">
      <span *ngFor="let label of groupLabels" class="label-chip" [ngStyle]="{'background': label.color, 'color': label.text_color, 'padding': '4px 12px', 'border-radius': '12px', 'font-size': '13px', 'font-weight': 500}">
        {{ label.name }}
      </span>
    </div> -->
    <div *ngIf="isSubmitting || commentSectionLoading" class="sidebar-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
      <mat-spinner diameter="48"></mat-spinner>
    </div>
    <ng-container *ngIf="!isSubmitting && !commentSectionLoading">
      <!-- Ticket Info Row (vertical, key-value, key bold/dark, value lighter) -->
      <div *ngIf="sidebarMode === 'edit' && selectedTicket" class="ticket-info-vertical">
        <div class="info-line"><span class="info-label">Ticket ID:</span> <span class="info-value">{{ selectedTicket.ticketId }}</span></div>
        <div class="info-line"><span class="info-label">Created by:</span> <span class="info-value">{{ selectedTicket.user }}</span></div>
        <div class="info-line"><span class="info-label">Date:</span> <span class="info-value">{{ selectedTicket.date | date: 'MMM d, y' }}</span></div>
        <div class="info-line">
          <span class="info-label">Status:</span>
          <span class="info-value status-badge" [ngClass]="selectedTicket.status">
            {{ selectedTicket.status | titlecase }}
          </span>
        </div>
      </div>
      <!-- Form Fields Section -->
      <div class="form-section">
        <div class="form-field-group">
          <label class="form-label">Title <span class="required">*</span></label>
          <input
            class="form-input"
            [class.error]="formErrors.title"
            type="text"
            [(ngModel)]="editTitle"
            (input)="onFieldChange('title')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Enter ticket title" />
          <div *ngIf="formErrors.title" class="error-message">{{ formErrors.title }}</div>
        </div>

        <div class="form-field-group">
          <label class="form-label">Labels <span class="optional-text">(optional)</span></label>
          <div class="labels-container" *ngIf="!isLoadingLabels">
            <div class="labels-dropdown">
              <div class="selected-labels" *ngIf="selectedLabels.length > 0">
                <mat-chip-list>
                  <mat-chip
                    *ngFor="let label of selectedLabels"
                    [removable]="true"
                    (removed)="toggleLabel(label)"
                    [style.background-color]="getLabelColor(label)"
                    [style.color]="getLabelTextColor(label)">
                    {{ label }}
                    <mat-icon matChipRemove>cancel</mat-icon>
                  </mat-chip>
                </mat-chip-list>
              </div>
              <mat-form-field appearance="outline" class="label-select">
                <mat-label>Select labels (optional)</mat-label>
                <mat-select multiple [(value)]="selectedLabels" [disabled]="sidebarMode === 'edit' && !isTicketOwner">
                  <mat-option *ngFor="let label of availableLabels" [value]="label.name">
                    <span [style.color]="label.color">{{ label.name }}</span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div *ngIf="isLoadingLabels" class="loading-labels">
            <mat-spinner diameter="20"></mat-spinner>
            <span>Loading labels...</span>
          </div>
        </div>

        <div class="form-field-group">
          <label class="form-label">Description</label>
          <textarea
            class="form-textarea"
            [class.error]="formErrors.message"
            rows="2"
            [(ngModel)]="editMessage"
            (input)="onFieldChange('message')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Describe your issue or request (optional)"></textarea>
          <div *ngIf="formErrors.message" class="error-message">{{ formErrors.message }}</div>
        </div>
      </div>
      <!-- Timeline Section -->
      <div class="timeline-section">
        <div class="timeline-header">
          <h3 class="timeline-title">Activity</h3>
        </div>

        <!-- Threaded Comments Section (Redesigned) -->
        <div class="threaded-comments-with-timeline" style="position: relative; display: flex;">
          <!-- Timeline Avatars & Line (absolute positioning) -->
          <div class="timeline-avatars-absolute">
            <ng-container *ngFor="let group of groupedComments; let i = index">
              <div
                class="timeline-avatar-abs-wrapper"
                [ngStyle]="{ top: parentOffsets[i] + 'px' }"
              >
                <div class="avatar-circle timeline-avatar">
                  <ng-container *ngIf="group.comments[0].avatarUrl; else timelineParentInitials">
                    <img [src]="group.comments[0].avatarUrl" alt="Avatar" />
                  </ng-container>
                  <ng-template #timelineParentInitials>
                    <span>{{ getAvatarText(group.comments[0].userName || group.comments[0].user) }}</span>
                  </ng-template>
                </div>
              </div>
            </ng-container>
            <div class="timeline-full-line" [ngStyle]="{ height: lastAvatarCenter + 'px' }"></div>
          </div>
          <!-- Comment Threads -->
          <div class="threaded-comments" style="margin-left: 48px;">
            <ng-container *ngFor="let group of groupedComments; let groupIndex = index">
              <div class="comment-thread-card" #parentCard>
                <!-- Parent Comment Header -->
                <div class="thread-header-row">
                  <div class="thread-avatar">
                    <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(group.comments[0].userName || group.comments[0].user)}">
                      <ng-container *ngIf="group.comments[0].avatarUrl; else parentInitials">
                        <img [src]="group.comments[0].avatarUrl" alt="Avatar" />
                      </ng-container>
                      <ng-template #parentInitials>
                        <span>{{ getAvatarText(group.comments[0].userName || group.comments[0].user) }}</span>
                      </ng-template>
                    </div>
                  </div>
                  <div class="thread-header-main">
                    <span class="thread-username">{{ logDisplayAuthor(group.comments[0]) }}</span>
                    <span class="thread-timestamp">{{ group.comments[0].timestamp | timeAgo }}</span>
                  </div>
                  <div class="thread-header-actions" style="margin-left:auto;">
                    <button mat-icon-button (click)="startReply(group.comments[0])"><mat-icon>reply</mat-icon></button>
                    <button mat-icon-button (click)="startEditComment(group.comments[0])" *ngIf="editingCommentId !== group.comments[0].id && canEditComment(group.comments[0])"><mat-icon>edit</mat-icon></button>
                    <button mat-icon-button (click)="deleteComment(group.comments[0])" *ngIf="canDeleteComment(group.comments[0])"><mat-icon>delete</mat-icon></button>
                  </div>
                </div>

                <!-- Parent Comment Body -->
                <div class="thread-body" *ngIf="editingCommentId !== group.comments[0].id">
                  <div [innerHTML]="(group.comments[0].text || group.comments[0].body) | safe:'html'"></div>
                </div>

                <!-- Edit Mode for Parent Comment -->
                <div class="thread-body" *ngIf="editingCommentId === group.comments[0].id">
                  <div class="comment-edit-container">
                    <quill-editor
                      [(ngModel)]="editingCommentContent"
                      [modules]="quillModules"
                      placeholder="Edit your comment..."
                      class="modern-quill-editor edit-mode">
                    </quill-editor>
                    <div class="edit-actions">
                      <button mat-button color="primary" (click)="saveEditedComment(group.comments[0])" [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                        <mat-icon>save</mat-icon>
                        {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                      </button>
                      <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                        <mat-icon>cancel</mat-icon>
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Collapse/Expand Button - Show only for discussions with multiple comments -->
                <div *ngIf="group.isDiscussion && group.comments.length > 1" class="collapse-replies-row">
                  <span class="collapse-replies-btn" (click)="toggleGroupCollapse(group)">
                    <mat-icon>{{ isGroupCollapsed(group) ? 'expand_more' : 'expand_less' }}</mat-icon>
                    {{ isGroupCollapsed(group) ? 'Expand replies' : 'Collapse replies' }}
                  </span>
                </div>

                <!-- Replies Section - Show only when not collapsed -->
                <div class="thread-replies" *ngIf="group.isDiscussion && group.comments.length > 1 && !isGroupCollapsed(group)">
                  <ng-container *ngFor="let reply of group.comments.slice(1); let replyIndex = index">
                    <div class="thread-reply-row" #lastCommentDiv="" *ngIf="groupIndex === groupedComments.length - 1 && replyIndex === group.comments.length - 2">
                      <div class="thread-avatar small">
                        <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                          <ng-container *ngIf="reply.avatarUrl; else replyInitials">
                            <img [src]="reply.avatarUrl" alt="Avatar" />
                          </ng-container>
                          <ng-template #replyInitials>
                            <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                          </ng-template>
                        </div>
                      </div>
                      <div class="thread-reply-main">
                        <div class="thread-reply-header">
                          <span class="thread-username">{{ logDisplayAuthor(reply) }}</span>
                          <span class="thread-timestamp">{{ reply.timestamp | timeAgo }}</span>
                          <span class="thread-reply-actions" style="margin-left:auto;">
                            <button mat-icon-button (click)="startEditComment(reply)" *ngIf="editingCommentId !== reply.id && canEditComment(reply)"><mat-icon>edit</mat-icon></button>
                            <button mat-icon-button (click)="deleteComment(reply)" *ngIf="canDeleteComment(reply)"><mat-icon>delete</mat-icon></button>
                          </span>
                        </div>
                        <div class="thread-reply-body" *ngIf="editingCommentId !== reply.id">
                          <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                        </div>

                        <!-- Edit Mode for Reply Comment -->
                        <div class="thread-reply-body" *ngIf="editingCommentId === reply.id">
                          <div class="comment-edit-container">
                            <quill-editor
                              [(ngModel)]="editingCommentContent"
                              [modules]="quillModules"
                              placeholder="Edit your reply..."
                              class="modern-quill-editor edit-mode">
                            </quill-editor>
                            <div class="edit-actions">
                              <button mat-button color="primary" (click)="saveEditedComment(reply)" [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                                <mat-icon>save</mat-icon>
                                {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                              </button>
                              <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                                <mat-icon>cancel</mat-icon>
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="thread-reply-row" *ngIf="!(groupIndex === groupedComments.length - 1 && replyIndex === group.comments.length - 2)">
                      <div class="thread-avatar small">
                        <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                          <ng-container *ngIf="reply.avatarUrl; else replyInitials">
                            <img [src]="reply.avatarUrl" alt="Avatar" />
                          </ng-container>
                          <ng-template #replyInitials>
                            <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                          </ng-template>
                        </div>
                      </div>
                      <div class="thread-reply-main">
                        <div class="thread-reply-header">
                          <span class="thread-username">{{ logDisplayAuthor(reply) }}</span>
                          <span class="thread-timestamp">{{ reply.timestamp | timeAgo }}</span>
                          <span class="thread-reply-actions" style="margin-left:auto;">
                            <button mat-icon-button (click)="startEditComment(reply)" *ngIf="editingCommentId !== reply.id && canEditComment(reply)"><mat-icon>edit</mat-icon></button>
                            <button mat-icon-button (click)="deleteComment(reply)" *ngIf="canDeleteComment(reply)"><mat-icon>delete</mat-icon></button>
                          </span>
                        </div>
                        <div class="thread-reply-body" *ngIf="editingCommentId !== reply.id">
                          <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                        </div>

                        <!-- Edit Mode for Reply Comment -->
                        <div class="thread-reply-body" *ngIf="editingCommentId === reply.id">
                          <div class="comment-edit-container">
                            <quill-editor
                              [(ngModel)]="editingCommentContent"
                              [modules]="quillModules"
                              placeholder="Edit your reply..."
                              class="modern-quill-editor edit-mode">
                            </quill-editor>
                            <div class="edit-actions">
                              <button mat-button color="primary" (click)="saveEditedComment(reply)" [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                                <mat-icon>save</mat-icon>
                                {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                              </button>
                              <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                                <mat-icon>cancel</mat-icon>
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>

                <!-- Reply Input & Resolve Thread -->
                <div class="thread-footer-row">
                  <input class="thread-reply-input" placeholder="Reply..." [(ngModel)]="replyContent[group.comments[0].id]" />
                  <button mat-button color="primary" class="thread-reply-btn" (click)="sendReply(group.comments[0])" [disabled]="!replyContent[group.comments[0].id] || isCommentSubmitting">Reply</button>
                  <button mat-button class="resolve-thread-btn" (click)="cancelReply()" [disabled]="isCommentSubmitting">Cancel</button>
                </div>

              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </ng-container>
  </div>


  <!-- Comment Input Section: Always visible -->
  <div class="comment-input-section">
    <div class="comment-input-container" [class.disabled]="isCommentSubmitting || !isCommentEditorEnabled">
      <div *ngIf="isCommentSubmitting" class="comment-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 40px;">
        <mat-spinner diameter="24"></mat-spinner>
      </div>
      <quill-editor
        [(ngModel)]="editorContent"
        [modules]="quillModules"
        (onEditorCreated)="onEditorCreated($event)"
        [placeholder]="sidebarMode === 'new' && !isCommentEditorEnabled ? 'Please fill in the title first...' : 'Leave a comment... Use @ to mention users...'"
        [disabled]="!isCommentEditorEnabled || isCommentSubmitting"
        class="modern-quill-editor">
      </quill-editor>

      <div class="pending-file-list" *ngIf="pendingFiles.length">
        <div class="pending-file-item" *ngFor="let file of pendingFiles; let i = index">
          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
          <span class="pending-file-name">{{ file.name }}</span>
          <button mat-icon-button color="warn" (click)="pendingFiles.splice(i, 1)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>
      <div class="comment-actions" style="position:absolute; bottom:12px; right:12px;">
        <button
          class="comment-send-btn"
          [disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          [class.disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          (click)="sendComment()">
          <mat-icon>send</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Footer: Always visible -->
  <div class="sidebar-footer">
    <button class="btn-secondary" (click)="onCancel()" [disabled]="isSubmitting">Cancel</button>
    <button
      class="btn-primary"
      (click)="saveTicket()"
      [disabled]="(sidebarMode === 'edit' && !isTicketOwner) || isSubmitting">
      <mat-icon *ngIf="isSubmitting" class="spinner">hourglass_empty</mat-icon>
      {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</div>

