import { <PERSON><PERSON><PERSON>, AfterView<PERSON><PERSON>t, ViewChild, ViewChildren, QueryList, ElementRef, OnInit, ChangeDetectorRef, NgZone, OnDestroy, AfterViewChecked } from '@angular/core';
import { AuthService } from '@auth0/auth0-angular';
import Quill from 'quill';
import { formatDate } from '@angular/common';
import { DiscussionCommentsService } from '../../service/ticket-view.service';
import { HelpCentreTicket, User, TabConfig } from './data';
import { Subscription } from 'rxjs';
import { UserService } from '../../service/user.service';
import { ActivatedRoute } from '@angular/router';

interface TicketComment {
  user: string;
  text: string;
  date: string;
}

@Component({
  selector: 'app-ticket-view',
  templateUrl: './ticket-view.component.html',
  styleUrls: ['./ticket-view.component.scss']
})
export class TicketViewComponent implements AfterViewInit, AfterView<PERSON>hecked, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  tabs: TabConfig[] = [
    { id: 'all', header: 'All Tickets', filterKey: 'all' },
    { id: 'my-tickets', header: 'My Tickets', filterKey: 'user' },
  ];
  tickets: (HelpCentreTicket & { comments?: TicketComment[] })[] = [];
  selectedTab: TabConfig = this.tabs[0];
  selectedTicketId: string | null = null;
  sidebarOpen: boolean = false;
  sidebarMode: 'edit' | 'new' | null = null;
  editTitle: string = '';
  editMessage: string = '';
  comment: string = '';
  selectedTicket: any | null = null;
  currentUser: any = { userName: '', userInitials: '', isCurrentUser: true };
  comments: any[] = [];
  editorContent: string = '';

  // Label functionality
  selectedLabels: string[] = [];
  isLoadingLabels: boolean = false;

  // Form validation states
  formErrors: { title?: string; message?: string } = {};
  isSubmitting: boolean = false;

  // Mention functionality properties
  choices: User[] = [];

  // Quill editor configuration
  quillModules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        ['superscript', 'subscript'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
        ['link', 'image', 'file'],
        ['clean']
      ],
      handlers: {
        image: () => this.selectLocalImage(),
        file: () => this.selectLocalFile(),
        link: () => this.insertLink(),
        superscript: () => this.toggleSuperscript(),
        subscript: () => this.toggleSubscript()
      }
    },
    clipboard: {
      matchVisual: false
    },
    keyboard: {
      bindings: {
        tab: {
          key: 9,
          handler: function() {
            return true;
          }
        }
      }
    }
  };

  quillEditor: any;
  mentionDropdown: any = null;

  // Add these properties to the class
  private mentionAtIndex: number | null = null;
  private mentionSearchTerm: string = '';

  commentSectionLoading = false;
  commentPollingSub: Subscription | undefined;

  isCommentSubmitting = false;

  // Add state for editing comments
  editingCommentId: number | null = null;
  editingCommentContent: string = '';

  // Add state for replying to comments
  replyingToCommentId: number | string | null = null;
  replyContent: { [commentId: string]: string } = {};

  // Add state for collapsed comment groups
  collapsedGroups: Set<string> = new Set();

  // Add grouped comments as a property instead of getter
  groupedComments: { isDiscussion: boolean; comments: any[] }[] = [];

  currentProjectId: string | null = null;
  currentGroupId: string | null = null;
  supplierList: any[] = [];
  selectedSupplier: any = null;

  subscriptionId: string | null = null;

  projectLabels: any[] = [];
  groupLabels: any[] = [];
  groupMembers: any[] = [];
  projectMembers: any[] = [];

  public privateToken: string = '';

  // Check if current user can edit a comment
  canEditComment(comment: any): boolean {
    // For now, allow editing if the comment has an id (meaning it's a real comment)
    // In a real application, you would check if the current user is the author
    return comment && comment.id && this.currentUser && this.currentUser.userName;
  }

  // Check if current user can delete a comment
  canDeleteComment(comment: any): boolean {
    // For now, allow deletion if the comment has an id (meaning it's a real comment)
    // In a real application, you would check if the current user is the author or has admin rights
    return comment && comment.id && this.currentUser && this.currentUser.userName;
  }

  onEditorCreated(quill) {
    this.quillEditor = quill;
    this.updateEditorState();
    this.setupMentionFunctionality();
  }

  // Update editor state based on whether it should be enabled
  updateEditorState() {
    if (this.quillEditor) {
      if (!this.isCommentEditorEnabled) {
        this.quillEditor.disable();
      } else {
        this.quillEditor.enable();
      }
    }
  }

  // Setup mention functionality within Quill
  setupMentionFunctionality() {
    if (!this.quillEditor) return;

    // Create mention dropdown
    this.createMentionDropdown();

    // Listen for text changes
    this.quillEditor.on('text-change', (delta, oldDelta, source) => {
      if (source === 'user') {
        this.handleTextChange();
      }
    });

    // Listen for keydown events
    this.quillEditor.on('keydown', (event) => {
      this.handleKeydown(event);
      this.handleEditModeKeydown(event);
    });
  }

  // Create mention dropdown
  createMentionDropdown() {
    // Remove existing dropdown if any
    if (this.mentionDropdown) {
      this.mentionDropdown.remove();
    }

    // Create dropdown element
    this.mentionDropdown = document.createElement('div');
    this.mentionDropdown.className = 'quill-mention-dropdown';
    this.mentionDropdown.style.cssText = `
      position: absolute;
      background: white;
      border: 1px solid #d0d7de;
      border-radius: 8px;
      box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
      max-height: 200px;
      overflow-y: auto;
      z-index: 10000;
      display: none;
      min-width: 200px;
    `;

    document.body.appendChild(this.mentionDropdown);
  }

  // Handle text changes to detect @ mentions
  handleTextChange() {
    const text = this.quillEditor.getText();
    const cursorPosition = this.quillEditor.getSelection()?.index || 0;

    // Find @ symbol before cursor
    const beforeCursor = text.substring(0, cursorPosition);
    const atIndex = beforeCursor.lastIndexOf('@');

    if (atIndex !== -1 && atIndex < cursorPosition) {
      const searchTerm = beforeCursor.substring(atIndex + 1);
      this.showMentionDropdown(searchTerm, atIndex);
    } else {
      this.hideMentionDropdown();
    }
  }

  // Handle keydown events
  handleKeydown(event) {
    if (this.mentionDropdown && this.mentionDropdown.style.display !== 'none') {
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp' || event.key === 'Enter' || event.key === 'Escape') {
        event.preventDefault();
        this.handleMentionKeydown(event);
      }
    }
  }

  // Handle keyboard shortcuts in edit mode
  handleEditModeKeydown(event) {
    if (this.editingCommentId) {
      // Ctrl/Cmd + Enter to save
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        const currentComment = this.comments.find(c => c.id === this.editingCommentId);
        if (currentComment && this.editingCommentContent.trim()) {
          this.saveEditedComment(currentComment);
        }
      }
      // Escape to cancel
      if (event.key === 'Escape') {
        event.preventDefault();
        this.cancelEditComment();
      }
    }
  }

  // Show mention dropdown
  showMentionDropdown(searchTerm: string, atIndex: number) {
    const filteredUsers = this.choices.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredUsers.length === 0) {
      this.hideMentionDropdown();
      return;
    }

    this.choices = filteredUsers;
    this.mentionAtIndex = atIndex;
    this.mentionSearchTerm = searchTerm;
    this.renderMentionDropdown();
    this.positionMentionDropdown(atIndex);
    this.mentionDropdown.style.display = 'block';
  }

  // Hide mention dropdown
  hideMentionDropdown() {
    if (this.mentionDropdown) {
      this.mentionDropdown.style.display = 'none';
    }
  }

  // Render mention dropdown content
  renderMentionDropdown() {
    this.mentionDropdown.innerHTML = '';

    this.choices.forEach((user, index) => {
      const item = document.createElement('div');
      item.className = 'mention-item';
      item.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 12px;
        transition: background-color 0.15s ease;
      `;

      item.innerHTML = `
        <div class="mention-avatar" style="
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: ${this.getAvatarColor(user.name)};
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 12px;
        ">${this.getAvatarText(user.name)}</div>
        <div class="mention-info">
          <div class="mention-name" style="font-weight: 600; color: #24292f; font-size: 14px;">${user.name}</div>
          <div class="mention-email" style="font-size: 12px; color: #656d76;">${user.email || ''}</div>
        </div>
      `;

      item.addEventListener('click', () => this.selectMention(user));
      item.addEventListener('mouseenter', () => {
        item.style.backgroundColor = '#f6f8fa';
      });
      item.addEventListener('mouseleave', () => {
        item.style.backgroundColor = 'transparent';
      });

      this.mentionDropdown.appendChild(item);
    });
  }

  // Position mention dropdown
  positionMentionDropdown(atIndex: number) {
    const editorBounds = this.quillEditor.container.getBoundingClientRect();
    const range = this.quillEditor.getBounds(atIndex);

    this.mentionDropdown.style.left = `${editorBounds.left + range.left}px`;
    this.mentionDropdown.style.top = `${editorBounds.top + range.bottom + 5}px`;
  }

  // Handle mention dropdown keydown
  handleMentionKeydown(event) {
    const items = this.mentionDropdown.querySelectorAll('.mention-item');
    const currentIndex = Array.from(items).findIndex(item => (item as HTMLElement).classList.contains('selected'));

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        this.selectMentionItem(items, nextIndex);
        break;
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        this.selectMentionItem(items, prevIndex);
        break;
      case 'Enter':
        event.preventDefault();
        if (currentIndex >= 0) {
          this.selectMention(this.choices[currentIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.hideMentionDropdown();
        break;
    }
  }

  // Select mention item visually
  selectMentionItem(items: NodeListOf<Element>, index: number) {
    items.forEach(item => (item as HTMLElement).classList.remove('selected'));
    if (items[index]) {
      (items[index] as HTMLElement).classList.add('selected');
      (items[index] as HTMLElement).style.backgroundColor = '#f6f8fa';
    }
  }

  // Select mention
  selectMention(user: User) {
    if (this.mentionAtIndex !== null) {
      const atIndex = this.mentionAtIndex;
      const searchTerm = this.mentionSearchTerm;
      const replacement = `@${user.name} `;

      // Focus and set selection
      this.quillEditor.focus();
      this.quillEditor.setSelection(atIndex + replacement.length, 0, 'user');

      // Delete the @searchTerm
      this.quillEditor.deleteText(atIndex, searchTerm.length + 1, 'user');

      // Insert the mention
      this.quillEditor.insertText(atIndex, replacement, {
        'color': '#0969da',
        'background': '#ddf4ff',
        'bold': true
      }, 'user');

      // Move the cursor after the mention
      this.quillEditor.setSelection(atIndex + replacement.length, 0, 'user');

      // Reset formatting after the mention
      this.quillEditor.format('color', false, 'user');
      this.quillEditor.format('background', false, 'user');
      this.quillEditor.format('bold', false, 'user');

      // Reset
      this.mentionAtIndex = null;
      this.mentionSearchTerm = '';
    }
    this.hideMentionDropdown();
  }

  // Helper to check if a file is an image
  isImage(filename: string): boolean {
    return /\.(png|jpe?g|gif|bmp|svg)$/i.test(filename);
  }

  // Store selected files before sending comment
  pendingFiles: { name: string; url: string; type: string }[] = [];

  selectLocalImage() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();
    input.onchange = () => {
      const file = input.files[0];
      if (file) {
        const fileUrl = URL.createObjectURL(file);
        const icon = '🖼️';
        const range = this.quillEditor.getSelection(true);
        this.quillEditor.clipboard.dangerouslyPasteHTML(
          range ? range.index : 0,
          `<a href='${fileUrl}' target='_blank' rel='noopener'>${icon} ${file.name}</a> `
        );
      }
    };
  }

  selectLocalFile() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', '.pdf,.doc,.docx,.txt,.xls,.xlsx,.csv,.zip,.rar,.7z,.json');
    input.click();
    input.onchange = () => {
      const file = input.files[0];
      if (file) {
        const fileUrl = URL.createObjectURL(file);
        const icon = '📄';
        const range = this.quillEditor.getSelection(true);
        this.quillEditor.clipboard.dangerouslyPasteHTML(
          range ? range.index : 0,
          `<a href='${fileUrl}' target='_blank' rel='noopener'>${icon} ${file.name}</a> `
        );
      }
    };
  }

  insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
      const range = this.quillEditor.getSelection();
      if (range) {
        if (range.length > 0) {
          // If text is selected, make it a link
          this.quillEditor.format('link', url);
        } else {
          // If no text is selected, insert the URL as a link
          this.quillEditor.insertText(range.index, url, 'link', url);
        }
      }
    }
  }

  toggleSuperscript() {
    if (this.quillEditor) {
      const range = this.quillEditor.getSelection();
      if (range) {
        const format = this.quillEditor.getFormat(range);
        const isSuperscript = format.script === 'super';

        if (isSuperscript) {
          // Remove superscript
          this.quillEditor.format('script', false);
        } else {
          // Apply superscript
          this.quillEditor.format('script', 'super');
        }
      }
    }
  }

  toggleSubscript() {
    if (this.quillEditor) {
      const range = this.quillEditor.getSelection();
      if (range) {
        const format = this.quillEditor.getFormat(range);
        const isSubscript = format.script === 'sub';

        if (isSubscript) {
          // Remove subscript
          this.quillEditor.format('script', false);
        } else {
          // Apply subscript
          this.quillEditor.format('script', 'sub');
        }
      }
    }
  }

  @ViewChild('chatList') chatList!: ElementRef;
  @ViewChildren('lastCommentDiv') lastCommentDivs!: QueryList<ElementRef>;
  @ViewChildren('parentCard') parentCardElements!: QueryList<ElementRef>;
  parentOffsets: number[] = [];
  private lastCount = 0;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private discussionCommentsService: DiscussionCommentsService,
    public auth: AuthService,
    private userService: UserService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    // Fetch subscription ID from query params
    this.route.queryParams.subscribe(params => {
      this.subscriptionId = params['sub'] || null;
      // Fetch current user's project and group id
      if (this.subscriptionId) {
        this.userService.me(this.subscriptionId).subscribe({
          next: (resp) => {
            this.currentProjectId = resp.result?.retailer_git_project_id || null;
            this.currentGroupId = resp.result?.retailer_git_group_id || null;
            this.loadTickets();
            this.loadLabelsAndMembers();
          },
          error: () => {
            this.loadTickets(); // fallback
          }
        });
      } else {
        this.loadTickets(); // fallback if no subscriptionId
      }
    });
    this.loadUsers();
    // Fetch the current user profile from Auth0
    this.auth.user$.subscribe(profile => {
      if (profile) {
        this.currentUser.userName =
          profile.name && profile.name.trim() !== ''
            ? profile.name.trim()
            : profile.username && profile.username.includes('@')
              ? profile.username.split('@')[0]
              : profile.username || profile.nickname || profile.email || '';
        this.currentUser.email = profile.email || '';
        this.currentUser.id = 2;
      }
    });

    // Register custom file icon for Quill
    const icons = Quill.import('ui/icons');
    icons['file'] = `<svg viewBox="0 0 18 18"><path d="M14.5 8.5v3.5a4.5 4.5 0 0 1-9 0v-7a2.5 2.5 0 0 1 5 0v7a.5.5 0 0 1-1 0v-7a1.5 1.5 0 0 0-3 0v7a3.5 3.5 0 0 0 7 0V8.5a.5.5 0 0 1 1 0z"/></svg>`;

    // Register custom superscript icon
    icons['superscript'] = `<svg viewBox="0 0 18 18">
      <text x="2" y="12" font-family="Arial" font-size="10" fill="currentColor">X</text>
      <text x="8" y="8" font-family="Arial" font-size="6" fill="currentColor">2</text>
    </svg>`;

    // Register custom subscript icon
    icons['subscript'] = `<svg viewBox="0 0 18 18">
      <text x="2" y="12" font-family="Arial" font-size="10" fill="currentColor">X</text>
      <text x="8" y="16" font-family="Arial" font-size="6" fill="currentColor">2</text>
    </svg>`;
  }

  loadTickets() {
    const projectId = this.getCurrentProjectId();
    if (!this.subscriptionId || !projectId) return;
    this.discussionCommentsService.getTickets(this.subscriptionId, projectId).subscribe({
      next: (apiTickets) => {
        if (!Array.isArray(apiTickets)) {
          this.tickets = [];
          return;
        }
        this.tickets = apiTickets.map(ticket => this.mapApiTicketToLocalFormat(ticket))
          .filter(Boolean) as (HelpCentreTicket & { comments?: TicketComment[] })[];
        this.tickets.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
      },
      error: (err) => {
        this.tickets = [];
      }
    });
  }

  loadUsers() {
    // Example: this.userService.getUsers().subscribe(users => { this.mentionUsers = users; });
    // If you don't have a users API, leave this empty or remove it
  }

  loadLabelsAndMembers() {
    const subscriptionId = this.subscriptionId;
    const projectId = this.currentProjectId;
    const groupId = this.currentGroupId;
    const privateToken = this.privateToken;

    console.log('Calling loadLabelsAndMembers with:', { subscriptionId, projectId, groupId, privateToken });

    if (subscriptionId && projectId) {
      this.discussionCommentsService.getProjectLabels(subscriptionId, projectId, privateToken)
        .subscribe(
          labels => {
            this.projectLabels = labels;
            console.log('Project Labels:', labels);
          },
          err => console.error('Project Labels Error:', err)
        );
      this.discussionCommentsService.getProjectMembers(subscriptionId, projectId, privateToken)
        .subscribe(
          members => {
            this.projectMembers = members;
            console.log('Project Members:', members);
          },
          err => console.error('Project Members Error:', err)
        );
    }
    if (subscriptionId && groupId) {
      this.discussionCommentsService.getGroupLabels(subscriptionId, groupId, privateToken)
        .subscribe(
          labels => {
            this.groupLabels = labels;
            console.log('Group Labels:', labels);
          },
          err => console.error('Group Labels Error:', err)
        );
      this.discussionCommentsService.getGroupMembers(subscriptionId, groupId, privateToken)
        .subscribe(
          members => {
            this.groupMembers = members;
            console.log('Group Members:', members);
          },
          err => console.error('Group Members Error:', err)
        );
    }
  }

  ngAfterViewInit() {
    this.calculateParentOffsets();
  }

  ngAfterViewChecked() {
    if (this.parentCardElements.length !== this.lastCount) {
      this.calculateParentOffsets();
      this.lastCount = this.parentCardElements.length;
    }
  }

  calculateParentOffsets() {
    setTimeout(() => {
      this.parentOffsets = this.parentCardElements.map(el => el.nativeElement.offsetTop);
      this.cdr.detectChanges();
    });
  }

  get allTicketsCount(): number {
    return this.tickets.length;
  }

  get myTicketsCount(): number {
    if (!this.currentUser || !this.currentUser.userName) {
      return 0;
    }
    return this.tickets.filter(ticket => ticket.user === this.currentUser.userName).length;
  }

  get filteredTickets(): any[] {
    if (this.selectedTab.filterKey === 'user') {
      if (!this.currentUser || !this.currentUser.userName) {
        return [];
      }
      return this.tickets.filter(ticket => ticket.user === this.currentUser.userName);
    }
    return this.tickets;
  }

  get isTicketOwner(): boolean {
    if (!this.selectedTicket || !this.currentUser || !this.currentUser.userName) {
      return false;
    }
    // Check both possible author fields for robustness
    return (
      this.selectedTicket.author?.username === this.currentUser.userName ||
      this.selectedTicket.user === this.currentUser.userName
    );
  }

  get isCommentEditorEnabled(): boolean {
    if (this.sidebarMode === 'new') {
      return this.editTitle.trim().length > 0;
    }
    return true; // Always enabled for edit mode
  }

  get selectedTicketComments(): TicketComment[] {
    if (!this.selectedTicket) return [];
    const ticket = this.tickets.find(t => t.id === this.selectedTicket!.id);
    return ticket?.comments || [];
  }

  selectTab(tab: TabConfig) {
    this.selectedTab = tab;
    this.selectedTicketId = null;
  }

  onTicketClick(ticket: any) {
    this.sidebarOpen = true;
    this.sidebarMode = 'edit';
    this.isSubmitting = true;
    this.commentSectionLoading = true;

    const projectId = this.getCurrentProjectId();
    const issueIid = ticket.iid;
    if (!this.subscriptionId || !projectId || !issueIid) return;
    const details$ = this.discussionCommentsService.getTicketDetails(this.subscriptionId, projectId, issueIid);
    const discussions$ = this.discussionCommentsService.getDiscussions(this.subscriptionId, projectId, issueIid);

    import('rxjs').then(rxjs => {
      rxjs.forkJoin([details$, discussions$]).subscribe({
        next: (results: [any, any[]]) => {
          const [detailedTicket, discussions] = results;
          this.selectedTicket = { ...detailedTicket, ...ticket };
          this.selectedTicketId = detailedTicket.id;
          this.editTitle = detailedTicket.title;
          this.editMessage = detailedTicket.description;
          this.selectedLabels = detailedTicket.labels || [];
          this.loadLabels();

          const ticketCreationEvent = {
            type: 'ticket-created',
            userName: this.selectedTicket.author.name,
            userInitials: this.getAvatarText(this.selectedTicket.author.name),
            avatarUrl: this.selectedTicket.author.avatar_url,
            text: `Ticket \"${this.selectedTicket.title}\" was created`,
            timestamp: this.selectedTicket.created_at,
            compact: true
          };
          let discussionNotes: any[] = [];
          if (discussions && discussions.length > 0) {
            this.selectedTicket.discussionId = discussions[0].id;
            // Flatten all notes from all discussions
            discussionNotes = this.flattenNotesFromDiscussions(discussions);
          }
          this.comments = [ticketCreationEvent, ...discussionNotes];
          this.updateGroupedComments();
          // this.renderLemonadeTimeline();

          this.isSubmitting = false;
          this.commentSectionLoading = false;
          this.cdr.detectChanges();
          setTimeout(() => this.scrollToLastComment(), 100);

          // Removed polling logic
          // this.startCommentPolling(this.selectedTicket);
        },
        error: (err) => {
          this.isSubmitting = false;
          this.commentSectionLoading = false;
          this.closeSidebar();
        }
      });
    });
  }

  onRaiseTicket() {
    this.selectedTicketId = null;
    this.selectedTicket = null;
    this.editTitle = '';
    this.editMessage = '';
    this.comment = '';
    this.editorContent = '';
    this.pendingFiles = [];
    this.formErrors = {};
    this.isSubmitting = false;
    this.comments = [];
    this.groupedComments = [];
    this.collapsedGroups.clear();
    this.selectedLabels = [];
    this.sidebarMode = 'new';
    this.sidebarOpen = true;
    this.loadLabels();
    setTimeout(() => this.scrollToLastComment(), 0);
  }

  closeSidebar() {
    this.sidebarOpen = false;
    this.sidebarMode = null;
    this.selectedTicket = null;
    this.selectedTicketId = null;
    this.editTitle = '';
    this.editMessage = '';
    this.comment = '';
    this.editorContent = '';
    this.pendingFiles = [];
    this.formErrors = {};
    this.isSubmitting = false;
    this.comments = [];
    this.groupedComments = [];
    this.collapsedGroups.clear();
    this.selectedLabels = [];
    // Removed polling logic
    // this.stopCommentPolling();
  }

  // Validate form fields
  private validateForm(): boolean {
    this.formErrors = {};
    let isValid = true;

    // Only title is mandatory
    if (!this.editTitle || this.editTitle.trim().length === 0) {
      this.formErrors.title = 'Title is required';
      isValid = false;
    } else if (this.editTitle.trim().length < 3) {
      this.formErrors.title = 'Title must be at least 3 characters long';
      isValid = false;
    }

    // Description is completely optional - no validation required
    // Labels are also optional - no validation required

    return isValid;
  }



  // Clear validation errors when user starts typing
  onFieldChange(field: 'title' | 'message') {
    // Clear validation error for this field
    if (this.formErrors[field]) {
      delete this.formErrors[field];
    }

    // Update editor state when title changes
    if (field === 'title') {
      this.updateEditorState();
    }
  }

  // Clear all validation errors
  clearValidationErrors() {
    this.formErrors = {};
  }

  // Load available labels (use existing projectLabels)
  loadLabels() {
    // Labels are already loaded by loadLabelsAndMembers() into this.projectLabels
    // This method is just for debugging or manual refresh
    console.log('Current projectLabels:', this.projectLabels);
    console.log('ProjectLabels length:', this.projectLabels.length);
  }

  // Toggle label selection
  toggleLabel(labelName: string) {
    const index = this.selectedLabels.indexOf(labelName);
    if (index > -1) {
      this.selectedLabels.splice(index, 1);
    } else {
      this.selectedLabels.push(labelName);
    }
  }

  // Remove label from selection
  removeLabel(labelName: string) {
    const index = this.selectedLabels.indexOf(labelName);
    if (index > -1) {
      this.selectedLabels.splice(index, 1);
    }
  }

  // Check if label is selected
  isLabelSelected(labelName: string): boolean {
    return this.selectedLabels.includes(labelName);
  }

  // Get label color
  getLabelColor(labelName: string): string {
    const label = this.projectLabels.find(l => l.name === labelName);
    return label?.color || this.getDefaultLabelColor(labelName);
  }

  // Get label text color
  getLabelTextColor(labelName: string): string {
    const label = this.projectLabels.find(l => l.name === labelName);
    return label?.text_color || this.getDefaultLabelTextColor(labelName);
  }

  // Get default label color if not found in availableLabels
  private getDefaultLabelColor(labelName: string): string {
    // Generate a consistent color based on label name
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];
    const hash = labelName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return colors[hash % colors.length];
  }

  // Get default label text color
  private getDefaultLabelTextColor(labelName: string): string {
    return '#ffffff'; // Default to white text for better contrast
  }

  // Handle label dropdown click
  onLabelDropdownClick() {
    console.log('Label dropdown clicked');
    console.log('Available labels:', this.projectLabels);
    console.log('Selected labels:', this.selectedLabels);

    // If no labels available, try to reload them
    if (this.projectLabels.length === 0) {
      console.log('No labels available, attempting to reload...');
      this.loadLabelsAndMembers();
    }
  }

  // Handle label dropdown open/close
  onLabelDropdownOpenChange(isOpen: boolean) {
    console.log('Label dropdown opened:', isOpen);
    if (isOpen && this.projectLabels.length === 0) {
      console.log('Dropdown opened but no labels, reloading...');
      this.loadLabelsAndMembers();
    }
  }

  // Test label dropdown functionality
  testLabelDropdown() {
    console.log('=== TESTING LABEL DROPDOWN ===');
    console.log('subscriptionId:', this.subscriptionId);
    console.log('currentProjectId:', this.currentProjectId);
    console.log('currentGroupId:', this.currentGroupId);
    console.log('privateToken:', this.privateToken);
    console.log('projectLabels:', this.projectLabels);
    console.log('selectedLabels:', this.selectedLabels);
    console.log('sidebarMode:', this.sidebarMode);
    console.log('isTicketOwner:', this.isTicketOwner);

    // Test manual label selection
    if (this.projectLabels.length > 0) {
      const firstLabel = this.projectLabels[0].name;
      console.log('Testing selection of first label:', firstLabel);
      if (!this.selectedLabels.includes(firstLabel)) {
        this.selectedLabels.push(firstLabel);
        console.log('Added label to selection');
      } else {
        console.log('Label already selected');
      }
    } else {
      console.log('No labels available to test with');
    }
  }

  // Handle cancel with unsaved changes check
  onCancel() {
    let hasChanges = false;
    if (this.sidebarMode === 'edit' && this.selectedTicket) {
      hasChanges = (
        this.editTitle.trim() !== (this.selectedTicket.title || '').trim() ||
        this.editMessage.trim() !== (this.selectedTicket.description || '').trim()
      );
    } else if (this.sidebarMode === 'new') {
      hasChanges = (
        this.editTitle.trim() !== '' ||
        this.editMessage.trim() !== ''
      );
    }

    if (hasChanges && !this.isSubmitting) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        this.closeSidebar();
      }
    } else {
      this.closeSidebar();
    }
  }

  saveTicket() {
    if (!this.validateForm()) {
      return;
    }
    this.isSubmitting = true;

    const projectId = this.getCurrentProjectId();
    if (!this.subscriptionId || !projectId) return;

    if (this.sidebarMode === 'new') {
      const payload = {
        title: this.editTitle,
        description: this.editMessage,
        supplier_project_id: projectId,
        labels: this.selectedLabels
      };
      this.discussionCommentsService.createTicket(this.subscriptionId, projectId, payload).subscribe({
        next: (newlyCreatedTicket) => {
          this.isSubmitting = false;
          this.sidebarMode = 'edit';
          const mappedTicket = this.mapApiTicketToLocalFormat(newlyCreatedTicket);
          this.selectedTicket = mappedTicket;
          this.selectedTicketId = mappedTicket.id;
          // Show loading spinner for comment section
          this.commentSectionLoading = true;
          // Fetch discussions for the new ticket
          this.discussionCommentsService.getDiscussions(this.subscriptionId, projectId, Number(mappedTicket.iid)).subscribe({
            next: (discussions) => {
              let discussionNotes: any[] = [];
              if (discussions && discussions.length > 0) {
                this.selectedTicket.discussionId = discussions[0].id;
                discussionNotes = this.flattenNotesFromDiscussions(discussions);
              }
              this.comments = discussionNotes;
              this.updateGroupedComments();
              this.commentSectionLoading = false;
              this.cdr.detectChanges();
              // Optionally refresh the main ticket list in the background
              this.loadTickets();
            },
            error: (err) => {
              this.commentSectionLoading = false;
            }
          });
        },
        error: (err) => {
          this.isSubmitting = false;
        },
      });
    } else if (this.sidebarMode === 'edit' && this.selectedTicket) {
      const payload = {
        title: this.editTitle,
        description: this.editMessage,
        labels: this.selectedLabels
      };
      this.discussionCommentsService.updateTicket(this.subscriptionId, projectId, Number(this.selectedTicket.iid), payload).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.closeSidebar();
          this.loadTickets(); // Refresh the list
        },
        error: (err) => {
          this.isSubmitting = false;
        },
      });
    }
  }

  // Helper function to map ticket data to avoid code duplication
  mapApiTicketToLocalFormat(ticket: any): HelpCentreTicket | null {
    if (!ticket) return null;
    // console.log('Mapping ticket:', ticket, 'Author:', ticket.author);
    return {
      id: ticket.id.toString(),
      iid: ticket.iid,
      project_id: ticket.project_id,
      title: ticket.title || 'No Title',
      message: ticket.description || '',
      description: ticket.description || '',
      user: ticket.author?.name || ticket.author?.username || ticket.user || '',
      author: ticket.author,
      date: ticket.created_at,
      updated_at: ticket.updated_at,
      status: ticket.state === 'closed' ? 'closed' : 'open',
      read: ticket.state ? !ticket.state.includes('opened') : true,
      ticketId: ticket.id?.toString() || ticket.iid?.toString() || 'N/A', // Use actual API ID
      type: 'comment', // Defaulting as API doesn't seem to provide this.
      labels: ticket.labels || [], // Include labels from API response
    };
  }

  // Optionally, add a method to log what is being used for display
  logDisplayAuthor(item: any) {
    const display = item.author?.name || item.author?.username || item.userName || item.user;
    // console.log('Display author for item:', item, '->', display);
    return display;
  }

  addComment() {
    const html = this.editorContent;
    if ((!html || !html.trim()) && this.pendingFiles.length === 0) return;

    // Get the HTML content from Quill editor
    let processedText = html;

    // Create new comment object with proper structure
    const newComment = {
      type: 'comment' as const,
      userName: this.currentUser.userName,
      userInitials: this.getAvatarText(this.currentUser.userName),
      avatarUrl: this.getAvatarUrl(this.currentUser.userName),
      text: processedText,
      timestamp: new Date().toISOString(),
      compact: true,
      files: this.pendingFiles.slice(),
    };

    // Add to comments array directly
    this.comments = [...this.comments, newComment];
    this.updateGroupedComments();

    // Also update the selectedTicket comments for persistence
    if (this.selectedTicket) {
      if (!this.selectedTicket.comments) {
        this.selectedTicket.comments = [];
      }
      this.selectedTicket.comments.push({
        user: this.currentUser.userName,
        text: processedText,
        date: new Date().toISOString(),
        files: this.pendingFiles.slice(),
      });
    }

    // Clear editor content, pending files, and mentions
    this.editorContent = '';
    this.pendingFiles = [];

    // Clear the Quill editor
    if (this.quillEditor) {
      this.quillEditor.setText('');
    }

    // Trigger change detection and scroll
    this.cdr.detectChanges();
    setTimeout(() => this.scrollToLastComment(), 100);
  }

  // Helper method to flatten notes from discussions
  private flattenNotesFromDiscussions(discussions: any[]): any[] {
    const allNotes: any[] = [];
    discussions.forEach(discussion => {
      if (Array.isArray(discussion.notes)) {
        discussion.notes.forEach(note => {
          // Only push if note has a valid id and body
          if (note && typeof note.id !== 'undefined' && typeof note.body !== 'undefined') {
            allNotes.push({
              ...note,
              discussionId: discussion.id, // will be undefined for top-level notes
              type: note.system ? 'system' : 'comment',
              userName: note.author?.name || '',
              userInitials: this.getAvatarText(note.author?.name || ''),
              avatarUrl: note.author?.avatar_url || '',
              text: note.body,
              timestamp: note.created_at,
              id: note.id,
            });
          }
        });
      }
    });
    return allNotes;
  }

  markTicketAsRead(ticketId: string) {
    const idx = this.tickets.findIndex(t => t.id === ticketId);
    if (idx !== -1) {
      this.tickets[idx] = { ...this.tickets[idx], read: true };
    }
  }

  // Helper to get a color for a user (for avatar background)
  getAvatarColor(name: string): string {
    // Simple hash to color
    const colors = ['#6a737d', '#2e5bff', '#43a047', '#fbc02d', '#e24329', '#1976d2', '#0366d6'];
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }

  // Helper to get avatar URL if present (simulate for demo)
  getAvatarUrl(name: string): string | null {
    // For demo, return null (or set a URL for specific users)
    if (name === 'Akash') return 'https://ui-avatars.com/api/?name=Akash&background=2e5bff&color=fff';
    if (name === 'Amit') return 'https://ui-avatars.com/api/?name=Amit&background=43a047&color=fff';
    return null;
  }



  // Helper for avatar text (first two letters, uppercase)
  getAvatarText(user: string): string {
    return user.slice(0, 2).toUpperCase();
  }

  scrollToBottom() {
    try {
      if (this.chatList && this.chatList.nativeElement) {
        this.chatList.nativeElement.scrollTop = this.chatList.nativeElement.scrollHeight;
      }
    } catch (e) {}
  }

  sendComment() {
    if (!this.editorContent || !this.selectedTicket) {
      return;
    }
    this.isCommentSubmitting = true;

    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    if (!this.subscriptionId || !projectId || !issueIid) return;
    const body = this.editorContent;

    const newComment = {
      type: 'comment',
      userName: this.currentUser.userName,
      userInitials: this.getAvatarText(this.currentUser.userName),
      avatarUrl: this.getAvatarUrl(this.currentUser.userName),
      text: body,
      timestamp: new Date().toISOString(),
      compact: true,
      // Ensure no discussionId for standalone
      discussionId: undefined,
    };

    const onSuccess = () => {
      this.editorContent = '';
      this.comments = [...this.comments, newComment];
      this.updateGroupedComments();
      this.isCommentSubmitting = false;
      setTimeout(() => this.scrollToLastComment(), 100);
      this.fetchDiscussionAfterNote();
    };

    // Always add as a new note, not as a reply to a discussion
    this.discussionCommentsService.addNoteToIssue(this.subscriptionId, projectId, issueIid, body).subscribe({
      next: onSuccess,
      error: (err) => {
        this.isCommentSubmitting = false;
      }
    });
  }

  // Helper to fetch discussion/comments after a note is added
  fetchDiscussionAfterNote() {
    if (!this.selectedTicket) return;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    if (!this.subscriptionId || !projectId || !issueIid) return;
    this.discussionCommentsService.getDiscussions(this.subscriptionId, projectId, issueIid).subscribe({
      next: (discussions) => {
        let discussionNotes: any[] = [];
        if (discussions && discussions.length > 0) {
          this.selectedTicket.discussionId = discussions[0].id;
          discussionNotes = this.flattenNotesFromDiscussions(discussions);
        }
        // Optionally, you can add the ticket creation event if needed
        this.comments = [...discussionNotes];
        this.updateGroupedComments();
        this.cdr.detectChanges();
        setTimeout(() => {
          this.scrollToLastComment();
        }, 100);
      },
      error: (err) => {
      }
    });
  }

  scrollToLastComment() {
    if (this.lastCommentDivs && this.lastCommentDivs.length > 0) {
      const lastDiv = this.lastCommentDivs.last;
      if (lastDiv && lastDiv.nativeElement) {
        lastDiv.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }
  }

  // Helper: Sort comments chronologically (oldest to newest)
  get sortedComments() {
    const filtered = this.comments
      .filter(c => !c.deleted && !c.is_deleted && c.state !== 'deleted')
      .slice()
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    return filtered;
  }

  // Helper: Get date label for separator
  getDateLabel(dateString: string): string {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);
    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();
    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';
    return formatDate(date, 'MMM d, yyyy', 'en-US');
  }

  // Helper: Should show date separator above this message?
  shouldShowDateSeparator(index: number, messages: any[]): boolean {
    if (index === 0) return false; // Don't show separator for the first message (ticket creation)
    const prev = messages[index - 1];
    const curr = messages[index];
    const prevDate = new Date(prev.timestamp).toDateString();
    const currDate = new Date(curr.timestamp).toDateString();
    return prevDate !== currDate;
  }

  // Cleanup method for mention dropdown
  ngOnDestroy() {
    if (this.mentionDropdown) {
      this.mentionDropdown.remove();
      this.mentionDropdown = null;
    }
    // Removed polling logic
    // this.stopCommentPolling();
  }

  // Add a comment using the new API
  addCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const commentBody = 'Your comment text here';
    this.discussionCommentsService
      .addComment(subscriptionId, projectId, issueIid, discussionId, commentBody)
      .subscribe({
        next: (response) => {
        },
        error: (err) => {
        }
      });
  }

  // Update a comment using the new API
  updateCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const noteId = 16; // The ID of the comment to update
    const updatedBody = 'Updated comment text';
    this.discussionCommentsService
              .updateCommentInDiscussionLegacy(subscriptionId, projectId, issueIid, discussionId, noteId, updatedBody)
      .subscribe({
        next: (response) => {
        },
        error: (err) => {
        }
      });
  }

  // Delete a comment using the new API
  deleteCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const noteId = 16; // The ID of the comment to delete
    this.discussionCommentsService
              .deleteCommentInDiscussionLegacy(subscriptionId, projectId, issueIid, discussionId, noteId)
      .subscribe({
        next: () => {
        },
        error: (err) => {
        }
      });
  }

  getDisplayUserName(item: any): string {
    // Try to match by username, user, or email
    const current = this.currentUser.userName?.toLowerCase() || '';
    const currentEmail = this.currentUser.email?.toLowerCase() || '';
    const itemUser = (item.userName || item.user || '').toLowerCase();
    const itemEmail = (item.email || '').toLowerCase();

    if (
      itemUser === current ||
      itemUser === currentEmail ||
      itemEmail === current ||
      itemEmail === currentEmail
    ) {
      return this.currentUser.userName;
    }
    // Fallback: show part before @ if it's an email
    if (itemUser.includes('@')) {
      return itemUser.split('@')[0];
    }
    return item.userName || item.user || 'Unknown';
  }

  // Start editing a comment
    startEditComment(item: any) {
    // If already editing another comment, cancel it first
    if (this.editingCommentId && this.editingCommentId !== item.id) {
      this.cancelEditComment();
    }

    this.editingCommentId = item.id;
    // Use the appropriate content field (text or body)
    this.editingCommentContent = item.text || item.body || '';

    // Focus the editor after a short delay to ensure it's rendered
    setTimeout(() => {
      if (this.quillEditor) {
        this.quillEditor.focus();
      }
    }, 100);
  }

  // Cancel editing
  cancelEditComment() {
    this.editingCommentId = null;
    this.editingCommentContent = '';

    // Clear any existing mention dropdown
    this.hideMentionDropdown();
  }

  // Save edited comment
  saveEditedComment(item: any) {
    if (!item.id) {
      return;
    }
    if (!this.selectedTicket || !this.editingCommentContent.trim()) return;

    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const noteId = item.id;
    const updatedBody = this.editingCommentContent;

    if (item.discussionId) {
      // Discussion note
      const discussionId = item.discussionId;
      this.discussionCommentsService.updateCommentInDiscussionLegacy(this.subscriptionId, projectId, issueIid, discussionId, noteId, updatedBody)
        .subscribe({
          next: () => {
            // Update both body and text fields to ensure consistency
            const idx = this.comments.findIndex((c: any) => c.id === item.id);
            if (idx !== -1) {
              this.comments[idx].body = updatedBody;
              this.comments[idx].text = updatedBody;
            }
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
            this.cdr.detectChanges();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    } else {
      // Top-level note
      this.discussionCommentsService.updateNoteOnIssue(this.subscriptionId, projectId, issueIid, noteId, updatedBody)
        .subscribe({
          next: () => {
            // Update both body and text fields to ensure consistency
            const idx = this.comments.findIndex((c: any) => c.id === item.id);
            if (idx !== -1) {
              this.comments[idx].body = updatedBody;
              this.comments[idx].text = updatedBody;
            }
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
            this.cdr.detectChanges();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    }
  }

  // Delete a comment
  deleteComment(item: any) {
    if (!item.id) {
      return;
    }
    if (!this.selectedTicket) return;
    if (!confirm('Are you sure you want to delete this comment?')) return;
    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const noteId = item.id;
    if (item.discussionId) {
      // Discussion note
      const discussionId = item.discussionId;
      this.discussionCommentsService.deleteCommentInDiscussionLegacy(this.subscriptionId, projectId, issueIid, discussionId, noteId)
        .subscribe({
          next: () => {
            this.comments = this.comments.filter((c: any) => c.id !== item.id);
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    } else {
      // Top-level note
      this.discussionCommentsService.deleteNoteOnIssue(this.subscriptionId, projectId, issueIid, noteId)
        .subscribe({
          next: () => {
            this.comments = this.comments.filter((c: any) => c.id !== item.id);
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    }
  }

    // Start replying to a comment
  startReply(comment: any) {
    this.replyingToCommentId = comment.id;
    this.replyContent[comment.id] = '';
  }

  // Start replying to the main ticket
  startReplyToTicket() {
    this.replyingToCommentId = 'ticket';
    this.replyContent['ticket'] = '';
  }

  // Cancel replying
  cancelReply() {
    if (this.replyingToCommentId) {
      delete this.replyContent[this.replyingToCommentId];
    }
    this.replyingToCommentId = null;
  }

  // Send a reply to a specific comment
  sendReply(parentComment: any) {
    const replyText = this.replyContent[parentComment.id];
    if (!replyText || !replyText.trim() || !this.selectedTicket) return;

    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const discussionId = parentComment.discussionId || parentComment.id;
    const replyBody = replyText;

    this.discussionCommentsService.addComment(this.subscriptionId, projectId, issueIid, discussionId, replyBody)
      .subscribe({
        next: (response) => {
          this.replyContent[parentComment.id] = '';
          this.fetchDiscussionAfterNote();
          this.isCommentSubmitting = false;
          this.cancelReply();
        },
        error: (err) => {
          this.isCommentSubmitting = false;
        }
      });
  }

  // Get unique key for a comment group
  getGroupKey(group: { isDiscussion: boolean; comments: any[] }): string {
    let groupKey: string;
    if (group.isDiscussion && group.comments.length > 0) {
      groupKey = `discussion-${group.comments[0].discussionId}`;
    } else if (group.comments.length > 0) {
      groupKey = `comment-${group.comments[0].id}`;
    } else {
      groupKey = `group-${Math.random()}`;
    }

    return groupKey;
  }

  // Check if a group is collapsed
  isGroupCollapsed(group: { isDiscussion: boolean; comments: any[] }): boolean {
    const groupKey = this.getGroupKey(group);
    return this.collapsedGroups.has(groupKey);
  }

  // Toggle collapse/expand for a group
  toggleGroupCollapse(group: { isDiscussion: boolean; comments: any[] }) {
    const groupKey = this.getGroupKey(group);
    if (this.collapsedGroups.has(groupKey)) {
      this.collapsedGroups.delete(groupKey);
    } else {
      this.collapsedGroups.add(groupKey);
    }
    this.cdr.detectChanges();
    setTimeout(() => this.calculateParentOffsets(), 0); // Ensure timeline updates after DOM changes
  }

  // Test method to verify button clicks
  testButtonClick() {
    alert('Button click is working!');
  }

  // Get total number of groups that can be collapsed
  getCollapsibleGroupsCount(): number {
    return this.groupedComments.filter(group =>
      group.isDiscussion && group.comments.length > 1
    ).length;
  }

  // Update grouped comments
  updateGroupedComments() {
    const groups: { isDiscussion: boolean; comments: any[] }[] = [];
    const seenDiscussions = new Set();
    for (const comment of this.sortedComments) {
      if (comment.discussionId) {
        if (!seenDiscussions.has(comment.discussionId)) {
          // Find all comments in this discussion, in order
          const thread = this.sortedComments.filter(
            c => c.discussionId === comment.discussionId
          );
          groups.push({ isDiscussion: true, comments: thread });
          seenDiscussions.add(comment.discussionId);
        }
      } else {
        groups.push({ isDiscussion: false, comments: [comment] });
      }
    }

    // If no comments exist, add some sample data for demonstration
    if (groups.length === 0) {
      groups.push(...this.getSampleThreadedComments());
    }

    this.groupedComments = groups;
  }

  // Group comments by discussionId for UI grouping
  getSampleThreadedComments(): { isDiscussion: boolean; comments: any[] }[] {
    // Implementation of getSampleThreadedComments method
    return [];
  }

  get lastAvatarCenter(): number {
    if (!this.parentOffsets.length) return 0;
    // 36 is the avatar height (px)
    return this.parentOffsets[this.parentOffsets.length - 1] + 18;
  }

  // Helper to get the current projectId dynamically
  getCurrentProjectId(): string | null {
    return this.selectedSupplier?.git_project_id || this.currentProjectId || null;
  }

  // Helper to get the current groupId dynamically
  getCurrentGroupId(): string | null {
    return this.currentGroupId || null;
  }

  // Helper to get the current issue iid dynamically
  getCurrentIssueIid(): number | null {
    return this.selectedTicket?.iid || null;
  }
}


